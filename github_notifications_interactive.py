#!/usr/bin/env python3
"""
GitHub 通知交互式管理工具
简化版本，方便直接使用
"""

import requests
import json
from datetime import datetime
from typing import List, Dict, Optional


class GitHubNotifications:
    """简化的 GitHub 通知管理器"""
    
    def __init__(self, token: str):
        self.token = token
        self.headers = {
            "Authorization": f"token {token}",
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "GitHub-Notification-Tool"
        }
        self.base_url = "https://api.github.com"
    
    def get_notifications(self, unread_only: bool = True, limit: int = 20) -> List[Dict]:
        """获取通知列表"""
        url = f"{self.base_url}/notifications"
        params = {
            "all": "false" if unread_only else "true",
            "per_page": limit
        }
        
        try:
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ 获取通知失败: {e}")
            return []
    
    def mark_as_read(self, notification_id: str) -> bool:
        """标记通知为已读"""
        url = f"{self.base_url}/notifications/threads/{notification_id}"
        
        try:
            response = requests.patch(url, headers=self.headers)
            response.raise_for_status()
            return True
        except Exception as e:
            print(f"❌ 标记已读失败: {e}")
            return False
    
    def mark_all_as_read(self) -> bool:
        """标记所有通知为已读"""
        url = f"{self.base_url}/notifications"
        data = {"last_read_at": datetime.utcnow().isoformat() + "Z"}
        
        try:
            response = requests.put(url, headers=self.headers, json=data)
            response.raise_for_status()
            return True
        except Exception as e:
            print(f"❌ 标记所有已读失败: {e}")
            return False
    
    def show_notifications(self, notifications: List[Dict]):
        """显示通知列表"""
        if not notifications:
            print("📭 没有通知")
            return
        
        print(f"\n📬 找到 {len(notifications)} 条通知:\n")
        
        for i, notif in enumerate(notifications, 1):
            title = notif.get('subject', {}).get('title', '无标题')
            repo = notif.get('repository', {}).get('full_name', '未知仓库')
            reason = notif.get('reason', '未知')
            updated = notif.get('updated_at', '')
            notif_id = notif.get('id', '')
            unread = notif.get('unread', False)
            
            # 格式化时间
            if updated:
                try:
                    dt = datetime.fromisoformat(updated.replace('Z', '+00:00'))
                    updated = dt.strftime('%m-%d %H:%M')
                except:
                    updated = updated[:10]
            
            status = "🔴" if unread else "✅"
            print(f"{i:2d}. {status} [{notif_id}]")
            print(f"    📝 {title}")
            print(f"    📁 {repo}")
            print(f"    🏷️  {reason} | ⏰ {updated}")
            print()


def main():
    """交互式主程序"""
    print("🚀 GitHub 通知管理工具")
    print("=" * 50)
    
    # 获取 Token
    token = input("请输入你的 GitHub Token: ").strip()
    if not token:
        print("❌ Token 不能为空")
        return
    
    gh = GitHubNotifications(token)
    
    while True:
        print("\n📋 选择操作:")
        print("1. 📬 查看未读通知")
        print("2. 📄 查看所有通知")
        print("3. ✅ 标记指定通知为已读")
        print("4. 🔄 标记所有通知为已读")
        print("5. 🚪 退出")
        
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == "1":
            print("\n🔍 获取未读通知...")
            notifications = gh.get_notifications(unread_only=True)
            gh.show_notifications(notifications)
            
        elif choice == "2":
            print("\n🔍 获取所有通知...")
            notifications = gh.get_notifications(unread_only=False)
            gh.show_notifications(notifications)
            
        elif choice == "3":
            notif_id = input("\n请输入通知ID: ").strip()
            if notif_id:
                print(f"⏳ 标记通知 {notif_id} 为已读...")
                if gh.mark_as_read(notif_id):
                    print("✅ 标记成功!")
                else:
                    print("❌ 标记失败!")
            else:
                print("❌ 通知ID不能为空")
                
        elif choice == "4":
            confirm = input("⚠️  确定要标记所有通知为已读吗? (y/N): ").strip().lower()
            if confirm == 'y':
                print("⏳ 标记所有通知为已读...")
                if gh.mark_all_as_read():
                    print("✅ 所有通知已标记为已读!")
                else:
                    print("❌ 操作失败!")
            else:
                print("❌ 操作已取消")
                
        elif choice == "5":
            print("👋 再见!")
            break
            
        else:
            print("❌ 无效选择，请重试")


if __name__ == "__main__":
    main()
