#!/usr/bin/env python3
"""
GitHub 通知管理工具
功能：
1. 获取 GitHub 通知
2. 显示通知详情
3. 标记指定通知为已读
4. 批量标记通知为已读
"""

import requests
import json
from datetime import datetime
from typing import List, Dict, Optional
import argparse


class GitHubNotificationManager:
    """GitHub 通知管理器"""
    
    def __init__(self, token: str):
        """
        初始化 GitHub 通知管理器
        
        Args:
            token: GitHub Personal Access Token
        """
        self.token = token
        self.base_url = "https://api.github.com"
        self.headers = {
            "Authorization": f"token {token}",
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "GitHub-Notification-Manager"
        }
    
    def get_notifications(self, all_notifications: bool = False, 
                         participating: bool = False, 
                         since: Optional[str] = None,
                         before: Optional[str] = None,
                         per_page: int = 50) -> List[Dict]:
        """
        获取 GitHub 通知
        
        Args:
            all_notifications: 是否包括已读通知
            participating: 是否只获取参与的通知
            since: 获取此时间之后的通知 (ISO 8601 格式)
            before: 获取此时间之前的通知 (ISO 8601 格式)
            per_page: 每页返回的通知数量
            
        Returns:
            通知列表
        """
        url = f"{self.base_url}/notifications"
        params = {
            "all": str(all_notifications).lower(),
            "participating": str(participating).lower(),
            "per_page": per_page
        }
        
        if since:
            params["since"] = since
        if before:
            params["before"] = before
            
        try:
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"获取通知失败: {e}")
            return []
    
    def get_notification_details(self, notification_id: str) -> Optional[Dict]:
        """
        获取单个通知的详细信息
        
        Args:
            notification_id: 通知ID
            
        Returns:
            通知详情或 None
        """
        url = f"{self.base_url}/notifications/threads/{notification_id}"
        
        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"获取通知详情失败: {e}")
            return None
    
    def mark_notification_as_read(self, notification_id: str) -> bool:
        """
        标记单个通知为已读
        
        Args:
            notification_id: 通知ID
            
        Returns:
            是否成功标记为已读
        """
        url = f"{self.base_url}/notifications/threads/{notification_id}"
        
        try:
            response = requests.patch(url, headers=self.headers)
            response.raise_for_status()
            return True
        except requests.exceptions.RequestException as e:
            print(f"标记通知为已读失败: {e}")
            return False
    
    def mark_all_notifications_as_read(self, last_read_at: Optional[str] = None) -> bool:
        """
        标记所有通知为已读
        
        Args:
            last_read_at: 标记此时间之前的通知为已读 (ISO 8601 格式)
            
        Returns:
            是否成功标记
        """
        url = f"{self.base_url}/notifications"
        data = {}
        
        if last_read_at:
            data["last_read_at"] = last_read_at
        else:
            data["last_read_at"] = datetime.utcnow().isoformat() + "Z"
            
        try:
            response = requests.put(url, headers=self.headers, json=data)
            response.raise_for_status()
            return True
        except requests.exceptions.RequestException as e:
            print(f"标记所有通知为已读失败: {e}")
            return False
    
    def mark_repository_notifications_as_read(self, owner: str, repo: str, 
                                            last_read_at: Optional[str] = None) -> bool:
        """
        标记指定仓库的所有通知为已读
        
        Args:
            owner: 仓库所有者
            repo: 仓库名称
            last_read_at: 标记此时间之前的通知为已读
            
        Returns:
            是否成功标记
        """
        url = f"{self.base_url}/repos/{owner}/{repo}/notifications"
        data = {}
        
        if last_read_at:
            data["last_read_at"] = last_read_at
        else:
            data["last_read_at"] = datetime.utcnow().isoformat() + "Z"
            
        try:
            response = requests.put(url, headers=self.headers, json=data)
            response.raise_for_status()
            return True
        except requests.exceptions.RequestException as e:
            print(f"标记仓库通知为已读失败: {e}")
            return False
    
    def display_notifications(self, notifications: List[Dict], show_details: bool = False):
        """
        显示通知列表
        
        Args:
            notifications: 通知列表
            show_details: 是否显示详细信息
        """
        if not notifications:
            print("没有找到通知")
            return
            
        print(f"\n找到 {len(notifications)} 条通知:")
        print("-" * 80)
        
        for i, notification in enumerate(notifications, 1):
            # 基本信息
            title = notification.get('subject', {}).get('title', 'N/A')
            repo_name = notification.get('repository', {}).get('full_name', 'N/A')
            reason = notification.get('reason', 'N/A')
            updated_at = notification.get('updated_at', 'N/A')
            notification_id = notification.get('id', 'N/A')
            unread = notification.get('unread', False)
            
            # 格式化时间
            if updated_at != 'N/A':
                try:
                    dt = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                    updated_at = dt.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    pass
            
            status = "🔴 未读" if unread else "✅ 已读"
            
            print(f"{i}. [{notification_id}] {status}")
            print(f"   标题: {title}")
            print(f"   仓库: {repo_name}")
            print(f"   原因: {reason}")
            print(f"   更新时间: {updated_at}")
            
            if show_details:
                subject_type = notification.get('subject', {}).get('type', 'N/A')
                url = notification.get('subject', {}).get('url', 'N/A')
                print(f"   类型: {subject_type}")
                print(f"   URL: {url}")
            
            print("-" * 80)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='GitHub 通知管理工具')
    parser.add_argument('--token', required=True, help='GitHub Personal Access Token')
    parser.add_argument('--action', choices=['list', 'read', 'read-all', 'read-repo'], 
                       default='list', help='执行的操作')
    parser.add_argument('--notification-id', help='通知ID (用于标记单个通知为已读)')
    parser.add_argument('--owner', help='仓库所有者 (用于标记仓库通知为已读)')
    parser.add_argument('--repo', help='仓库名称 (用于标记仓库通知为已读)')
    parser.add_argument('--all', action='store_true', help='包括已读通知')
    parser.add_argument('--participating', action='store_true', help='只显示参与的通知')
    parser.add_argument('--details', action='store_true', help='显示详细信息')
    parser.add_argument('--per-page', type=int, default=50, help='每页通知数量')
    
    args = parser.parse_args()
    
    # 创建通知管理器
    manager = GitHubNotificationManager(args.token)
    
    if args.action == 'list':
        # 获取并显示通知
        notifications = manager.get_notifications(
            all_notifications=args.all,
            participating=args.participating,
            per_page=args.per_page
        )
        manager.display_notifications(notifications, show_details=args.details)
        
    elif args.action == 'read':
        # 标记单个通知为已读
        if not args.notification_id:
            print("错误: 需要提供 --notification-id 参数")
            return
            
        success = manager.mark_notification_as_read(args.notification_id)
        if success:
            print(f"通知 {args.notification_id} 已标记为已读")
        else:
            print(f"标记通知 {args.notification_id} 为已读失败")
            
    elif args.action == 'read-all':
        # 标记所有通知为已读
        success = manager.mark_all_notifications_as_read()
        if success:
            print("所有通知已标记为已读")
        else:
            print("标记所有通知为已读失败")
            
    elif args.action == 'read-repo':
        # 标记仓库通知为已读
        if not args.owner or not args.repo:
            print("错误: 需要提供 --owner 和 --repo 参数")
            return
            
        success = manager.mark_repository_notifications_as_read(args.owner, args.repo)
        if success:
            print(f"仓库 {args.owner}/{args.repo} 的所有通知已标记为已读")
        else:
            print(f"标记仓库 {args.owner}/{args.repo} 通知为已读失败")


if __name__ == "__main__":
    main()
