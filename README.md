# GitHub 通知管理工具

这是一个用于管理 GitHub 通知的 Python 工具集，支持获取通知、查看详情和标记已读等功能。

## 功能特性

- 📬 获取 GitHub 通知（未读/全部）
- 📋 显示通知详细信息
- ✅ 标记单个通知为已读
- 🔄 批量标记通知为已读
- 📊 按仓库和原因分组显示通知
- 🎯 交互式命令行界面

## 安装依赖

```bash
pip install -r requirements.txt
```

## 获取 GitHub Token

1. 访问 [GitHub Settings > Personal access tokens](https://github.com/settings/tokens)
2. 点击 "Generate new token (classic)"
3. 选择以下权限：
   - `notifications` - 读取和标记通知
   - `repo` - 访问仓库信息（如果需要）
4. 复制生成的 token

## 使用方法

### 1. 交互式界面（推荐）

```bash
python github_notifications_interactive.py
```

这会启动一个友好的交互式界面，按提示操作即可。

### 2. 命令行工具

```bash
# 查看未读通知
python github_notifications.py --token YOUR_TOKEN --action list

# 查看所有通知（包括已读）
python github_notifications.py --token YOUR_TOKEN --action list --all

# 显示详细信息
python github_notifications.py --token YOUR_TOKEN --action list --details

# 标记特定通知为已读
python github_notifications.py --token YOUR_TOKEN --action read --notification-id NOTIFICATION_ID

# 标记所有通知为已读
python github_notifications.py --token YOUR_TOKEN --action read-all

# 标记特定仓库的通知为已读
python github_notifications.py --token YOUR_TOKEN --action read-repo --owner OWNER --repo REPO_NAME
```

### 3. 在代码中使用

```python
from github_notifications_interactive import GitHubNotifications

# 创建通知管理器
gh = GitHubNotifications("your_github_token")

# 获取未读通知
notifications = gh.get_notifications(unread_only=True)

# 显示通知
gh.show_notifications(notifications)

# 标记通知为已读
if notifications:
    notification_id = notifications[0]['id']
    success = gh.mark_as_read(notification_id)
    print("标记成功!" if success else "标记失败!")
```

## 文件说明

- `github_notifications.py` - 完整的命令行工具，支持所有功能
- `github_notifications_interactive.py` - 简化的交互式工具，易于使用
- `example_usage.py` - 使用示例代码
- `requirements.txt` - Python 依赖包列表

## 通知类型说明

GitHub 通知的常见原因（reason）：

- `assign` - 被分配到 issue 或 PR
- `author` - 你创建的 issue 或 PR 有新活动
- `comment` - 有人评论了你参与的 issue 或 PR
- `mention` - 有人在评论中提到了你
- `review_requested` - 被请求审查 PR
- `state_change` - issue 或 PR 状态发生变化
- `subscribed` - 你订阅的 issue 或 PR 有新活动

## 注意事项

1. **Token 安全**：不要将 GitHub Token 提交到代码仓库中
2. **API 限制**：GitHub API 有速率限制，避免频繁调用
3. **权限要求**：确保 Token 有 `notifications` 权限
4. **网络连接**：需要稳定的网络连接访问 GitHub API

## 故障排除

### 常见错误

1. **401 Unauthorized**
   - 检查 Token 是否正确
   - 确认 Token 有足够的权限

2. **403 Forbidden**
   - 可能触发了 API 速率限制
   - 等待一段时间后重试

3. **404 Not Found**
   - 检查通知 ID 是否正确
   - 确认仓库名称是否正确

### 调试技巧

- 使用 `--details` 参数查看更多信息
- 检查网络连接
- 验证 Token 权限设置

## 示例输出

```
🚀 GitHub 通知管理工具
==================================================

📋 选择操作:
1. 📬 查看未读通知
2. 📄 查看所有通知
3. ✅ 标记指定通知为已读
4. 🔄 标记所有通知为已读
5. 🚪 退出

请选择 (1-5): 1

🔍 获取未读通知...

📬 找到 3 条通知:

 1. 🔴 [123456789]
    📝 Fix bug in user authentication
    📁 username/awesome-project
    🏷️  mention | ⏰ 12-25 14:30

 2. 🔴 [123456790]
    📝 Add new feature request
    📁 username/another-project
    🏷️  assign | ⏰ 12-25 13:15
```

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个工具！
