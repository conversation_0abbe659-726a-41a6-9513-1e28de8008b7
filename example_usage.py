#!/usr/bin/env python3
"""
GitHub 通知管理使用示例
展示如何在代码中使用 GitHub 通知功能
"""

from github_notifications_interactive import GitHubNotifications


def example_basic_usage():
    """基础使用示例"""
    # 替换为你的 GitHub Token
    TOKEN = "****************************************"
    
    # 创建通知管理器
    gh = GitHubNotifications(TOKEN)
    
    print("=== 基础使用示例 ===")
    
    # 1. 获取未读通知
    print("\n1. 获取未读通知:")
    unread_notifications = gh.get_notifications(unread_only=True, limit=10)
    gh.show_notifications(unread_notifications)
    
    # 2. 获取所有通知（包括已读）
    print("\n2. 获取所有通知:")
    all_notifications = gh.get_notifications(unread_only=False, limit=5)
    gh.show_notifications(all_notifications)
    
    # 3. 标记特定通知为已读（需要真实的通知ID）
    if unread_notifications:
        first_notification_id = unread_notifications[0].get('id')
        print(f"\n3. 标记通知 {first_notification_id} 为已读:")
        success = gh.mark_as_read(first_notification_id)
        if success:
            print("✅ 标记成功!")
        else:
            print("❌ 标记失败!")


def example_batch_operations():
    """批量操作示例"""
    TOKEN = "your_github_token_here"
    gh = GitHubNotifications(TOKEN)
    
    print("\n=== 批量操作示例 ===")
    
    # 获取未读通知
    notifications = gh.get_notifications(unread_only=True)
    
    if not notifications:
        print("📭 没有未读通知")
        return
    
    print(f"📬 找到 {len(notifications)} 条未读通知")
    
    # 批量标记前几条为已读
    batch_size = min(3, len(notifications))
    print(f"\n批量标记前 {batch_size} 条通知为已读:")
    
    for i in range(batch_size):
        notif = notifications[i]
        notif_id = notif.get('id')
        title = notif.get('subject', {}).get('title', '无标题')
        
        print(f"  标记: {title[:50]}...")
        success = gh.mark_as_read(notif_id)
        if success:
            print("    ✅ 成功")
        else:
            print("    ❌ 失败")


def example_filter_notifications():
    """过滤通知示例"""
    TOKEN = "your_github_token_here"
    gh = GitHubNotifications(TOKEN)
    
    print("\n=== 过滤通知示例 ===")
    
    # 获取所有通知
    notifications = gh.get_notifications(unread_only=False, limit=50)
    
    if not notifications:
        print("📭 没有通知")
        return
    
    # 按仓库分组
    repo_groups = {}
    for notif in notifications:
        repo_name = notif.get('repository', {}).get('full_name', '未知仓库')
        if repo_name not in repo_groups:
            repo_groups[repo_name] = []
        repo_groups[repo_name].append(notif)
    
    print("📊 按仓库分组的通知:")
    for repo, notifs in repo_groups.items():
        unread_count = sum(1 for n in notifs if n.get('unread', False))
        total_count = len(notifs)
        print(f"  📁 {repo}: {unread_count}/{total_count} 未读")
    
    # 按原因分组
    reason_groups = {}
    for notif in notifications:
        reason = notif.get('reason', '未知')
        if reason not in reason_groups:
            reason_groups[reason] = 0
        reason_groups[reason] += 1
    
    print("\n📊 按原因分组的通知:")
    for reason, count in reason_groups.items():
        print(f"  🏷️  {reason}: {count} 条")


def example_notification_details():
    """通知详情示例"""
    TOKEN = "your_github_token_here"
    gh = GitHubNotifications(TOKEN)
    
    print("\n=== 通知详情示例 ===")
    
    # 获取通知
    notifications = gh.get_notifications(unread_only=True, limit=5)
    
    if not notifications:
        print("📭 没有未读通知")
        return
    
    # 显示详细信息
    for i, notif in enumerate(notifications[:3], 1):  # 只显示前3条
        print(f"\n📋 通知 {i} 详情:")
        print(f"  ID: {notif.get('id', 'N/A')}")
        print(f"  标题: {notif.get('subject', {}).get('title', 'N/A')}")
        print(f"  仓库: {notif.get('repository', {}).get('full_name', 'N/A')}")
        print(f"  类型: {notif.get('subject', {}).get('type', 'N/A')}")
        print(f"  原因: {notif.get('reason', 'N/A')}")
        print(f"  未读: {'是' if notif.get('unread', False) else '否'}")
        print(f"  更新时间: {notif.get('updated_at', 'N/A')}")
        
        # 如果有URL，显示相关链接
        subject_url = notif.get('subject', {}).get('url')
        if subject_url:
            print(f"  相关链接: {subject_url}")


if __name__ == "__main__":
    print("🚀 GitHub 通知管理使用示例")
    print("=" * 50)
    print("⚠️  请先在代码中设置你的 GitHub Token")
    print()
    
    # 运行示例（需要先设置 TOKEN）
    try:
        example_basic_usage()
        example_batch_operations()
        example_filter_notifications()
        example_notification_details()
    except Exception as e:
        print(f"❌ 运行示例时出错: {e}")
        print("请确保已正确设置 GitHub Token")
